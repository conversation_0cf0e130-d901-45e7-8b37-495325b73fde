<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attendance', function (Blueprint $table) {
            // Primary key
            $table->id();
            
            // Foreign key relationships
            $table->foreignId('student_id')
                  ->constrained('students')
                  ->onDelete('cascade')
                  ->comment('References students.id');
                  
            $table->foreignId('teacher_id')
                  ->constrained('teachers')
                  ->onDelete('cascade')
                  ->comment('References teachers.id');
                  
            $table->foreignId('subject_id')
                  ->constrained('subjects')
                  ->onDelete('cascade')
                  ->comment('References subjects.id');
            
            // Date and time fields
            $table->date('date');
            $table->time('time_in')->nullable();
            $table->time('time_out')->nullable();
            
            // Status tracking
            $table->enum('status', ['present', 'absent', 'late', 'excused'])
                  ->default('present');
            
            // Additional information
            $table->text('remarks')->nullable();
            $table->string('ip_address')->nullable();
            $table->json('device_info')->nullable();
            
            // Timestamps
            $table->timestamps();
            
            // Composite indexes for efficient querying
            $table->index(['date', 'student_id']); // Date-student queries
            $table->index(['date', 'teacher_id']); // Date-teacher queries
            $table->index(['student_id', 'date']); // Student attendance history
            $table->index(['teacher_id', 'date']); // Teacher attendance records
            $table->index(['subject_id', 'date']); // Subject attendance records
            $table->index(['date', 'status']); // Daily status reports
            $table->index('status'); // Status filtering
            $table->index('created_at'); // Chronological queries
            
            // Unique constraint to prevent duplicate attendance records
            $table->unique(['student_id', 'teacher_id', 'subject_id', 'date'], 
                          'unique_attendance_record');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attendance');
    }
};
