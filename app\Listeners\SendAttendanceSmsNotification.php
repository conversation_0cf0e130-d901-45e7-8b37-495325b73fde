<?php

namespace App\Listeners;

use App\Enums\AttendanceStatus;
use App\Events\AttendanceRecorded;
use App\Services\SmsService;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendAttendanceSmsNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The SMS service instance.
     */
    protected SmsService $smsService;

    /**
     * Create the event listener.
     */
    public function __construct(SmsService $smsService)
    {
        $this->smsService = $smsService;
    }

    /**
     * Handle the event.
     */
    public function handle(AttendanceRecorded $event): void
    {
        $attendance = $event->attendance;
        $student = $attendance->student;
        $subject = $attendance->subject;

        // Only send SMS for certain statuses and if parent phone exists
        if (!$this->shouldSendSms($attendance) || !$student->parent_phone) {
            return;
        }

        try {
            $message = $this->buildSmsMessage($attendance);
            
            $this->smsService->send(
                $student->formatted_phone,
                $message
            );

            Log::info('Attendance SMS sent', [
                'student_id' => $student->id,
                'attendance_id' => $attendance->id,
                'status' => $attendance->status->value,
                'phone' => $student->formatted_phone,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send attendance SMS', [
                'student_id' => $student->id,
                'attendance_id' => $attendance->id,
                'error' => $e->getMessage(),
            ]);

            // Optionally retry or handle the failure
            $this->fail($e);
        }
    }

    /**
     * Determine if SMS should be sent for this attendance record.
     */
    protected function shouldSendSms(Attendance $attendance): bool
    {
        // Send SMS for absent, late, or when marking present after being absent
        return in_array($attendance->status, [
            AttendanceStatus::ABSENT,
            AttendanceStatus::LATE,
            AttendanceStatus::PRESENT, // When student arrives
        ]);
    }

    /**
     * Build the SMS message based on attendance status.
     */
    protected function buildSmsMessage(Attendance $attendance): string
    {
        $student = $attendance->student;
        $subject = $attendance->subject;
        $date = $attendance->date->format('M j, Y');
        $timeIn = $attendance->time_in ? Carbon::parse($attendance->time_in)->format('h:i A') : null;

        $schoolName = config('app.school_name', 'School');

        return match($attendance->status) {
            AttendanceStatus::PRESENT => 
                "Good day! Your child {$student->full_name} has arrived at {$schoolName} for {$subject->name} on {$date} at {$timeIn}. - {$schoolName}",
            
            AttendanceStatus::LATE => 
                "NOTICE: Your child {$student->full_name} arrived late at {$schoolName} for {$subject->name} on {$date} at {$timeIn}. Please ensure punctuality. - {$schoolName}",
            
            AttendanceStatus::ABSENT => 
                "ALERT: Your child {$student->full_name} is marked absent for {$subject->name} on {$date}. Please contact the school if this is incorrect. - {$schoolName}",
            
            AttendanceStatus::EXCUSED => 
                "INFO: Your child {$student->full_name} has an excused absence for {$subject->name} on {$date}. - {$schoolName}",
        };
    }

    /**
     * Handle a job failure.
     */
    public function failed(AttendanceRecorded $event, \Throwable $exception): void
    {
        Log::error('Attendance SMS notification job failed', [
            'attendance_id' => $event->attendance->id,
            'student_id' => $event->attendance->student_id,
            'error' => $exception->getMessage(),
        ]);
    }
}
