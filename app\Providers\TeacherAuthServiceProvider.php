<?php

namespace App\Providers;

use App\Http\Middleware\TeacherAuth;
use App\Http\Middleware\TeacherPermission;
use App\Http\Middleware\TeacherRole;
use App\Http\Middleware\TeacherSectionAccess;
use Illuminate\Routing\Router;
use Illuminate\Support\ServiceProvider;

class TeacherAuthServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $router = $this->app->make(Router::class);
        
        // Register middleware
        $router->aliasMiddleware('teacher.auth', TeacherAuth::class);
        $router->aliasMiddleware('teacher.permission', TeacherPermission::class);
        $router->aliasMiddleware('teacher.role', TeacherRole::class);
        $router->aliasMiddleware('teacher.section', TeacherSectionAccess::class);
        
        // Register middleware groups
        $router->middlewareGroup('teacher', [
            'teacher.auth',
        ]);
        
        $router->middlewareGroup('teacher.admin', [
            'teacher.auth',
            'teacher.role:admin,principal',
        ]);
        
        $router->middlewareGroup('teacher.principal', [
            'teacher.auth',
            'teacher.role:principal',
        ]);
    }
}
