<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('students', function (Blueprint $table) {
            // Primary key
            $table->id();
            
            // Student identification
            $table->string('student_id')->unique()->comment('DepEd student ID');
            
            // Personal information
            $table->string('first_name', 100);
            $table->string('last_name', 100);
            $table->string('middle_name', 100)->nullable();
            
            // Academic information
            $table->enum('grade_level', ['11', '12']);
            $table->string('section', 50);
            
            // Contact information
            $table->string('parent_phone', 15);
            $table->string('emergency_contact', 15)->nullable();
            
            // QR code and photo
            $table->string('qr_code_hash', 255)->unique();
            $table->string('photo_path')->nullable();
            
            // Status tracking
            $table->enum('status', ['active', 'inactive', 'transferred', 'graduated'])
                  ->default('active');
            
            // Timestamps
            $table->timestamps();
            
            // Indexes for performance optimization
            $table->index(['grade_level', 'section']); // Common query combination
            $table->index('status'); // Frequently filtered by status
            $table->index(['last_name', 'first_name']); // Name searches
            $table->index('created_at'); // Date-based queries
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('students');
    }
};
