<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class TeacherPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$permissions): Response
    {
        $teacher = Auth::guard('teacher')->user();

        if (!$teacher) {
            if ($request->expectsJson()) {
                return response()->json(['message' => 'Unauthenticated.'], 401);
            }
            
            return redirect()->route('teacher.login');
        }

        // Check if teacher has any of the required permissions
        if (!empty($permissions) && !$teacher->hasAnyPermission($permissions)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Insufficient permissions.',
                    'required_permissions' => $permissions,
                    'user_permissions' => $teacher->getAllPermissions(),
                ], 403);
            }
            
            abort(403, 'Insufficient permissions to access this resource.');
        }

        return $next($request);
    }
}
