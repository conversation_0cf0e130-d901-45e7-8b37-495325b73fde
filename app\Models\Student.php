<?php

namespace App\Models;

use App\Enums\StudentStatus;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class Student extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'student_id',
        'first_name',
        'last_name',
        'middle_name',
        'grade_level',
        'section',
        'parent_phone',
        'emergency_contact',
        'photo_path',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'qr_code_hash',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'status' => StudentStatus::class,
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        static::creating(function (Student $student) {
            if (empty($student->qr_code_hash)) {
                $student->generateQRCode();
            }
        });
    }

    // RELATIONSHIPS

    /**
     * Get the attendance records for the student.
     */
    public function attendance(): HasMany
    {
        return $this->hasMany(Attendance::class);
    }

    /**
     * Get the teachers associated with the student through sections.
     */
    public function teachers(): BelongsToMany
    {
        return $this->belongsToMany(Teacher::class, 'student_teacher_sections')
                    ->withPivot('section', 'subject_id')
                    ->withTimestamps();
    }

    // ACCESSORS

    /**
     * Get the student's full name.
     */
    public function getFullNameAttribute(): string
    {
        $parts = array_filter([
            $this->first_name,
            $this->middle_name,
            $this->last_name,
        ]);

        return implode(' ', $parts);
    }

    /**
     * Get the formatted phone number with country code.
     */
    public function getFormattedPhoneAttribute(): string
    {
        if (empty($this->parent_phone)) {
            return '';
        }

        // Assume Philippines country code if not present
        $phone = preg_replace('/[^0-9]/', '', $this->parent_phone);
        
        if (strlen($phone) === 10 && !str_starts_with($phone, '63')) {
            $phone = '63' . $phone;
        }

        return '+' . $phone;
    }

    // MUTATORS

    /**
     * Set the first name attribute (capitalize).
     */
    public function setFirstNameAttribute(string $value): void
    {
        $this->attributes['first_name'] = Str::title(trim($value));
    }

    /**
     * Set the last name attribute (capitalize).
     */
    public function setLastNameAttribute(string $value): void
    {
        $this->attributes['last_name'] = Str::title(trim($value));
    }

    /**
     * Set the middle name attribute (capitalize).
     */
    public function setMiddleNameAttribute(?string $value): void
    {
        $this->attributes['middle_name'] = $value ? Str::title(trim($value)) : null;
    }

    /**
     * Set the parent phone attribute (format).
     */
    public function setParentPhoneAttribute(string $value): void
    {
        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $value);
        
        // Store the cleaned phone number
        $this->attributes['parent_phone'] = $phone;
    }

    /**
     * Set the emergency contact attribute (format).
     */
    public function setEmergencyContactAttribute(?string $value): void
    {
        if ($value) {
            // Remove all non-numeric characters
            $phone = preg_replace('/[^0-9]/', '', $value);
            $this->attributes['emergency_contact'] = $phone;
        } else {
            $this->attributes['emergency_contact'] = null;
        }
    }

    // SCOPES

    /**
     * Scope a query to only include active students.
     */
    public function scopeActive(Builder $query): void
    {
        $query->where('status', StudentStatus::ACTIVE);
    }

    /**
     * Scope a query to filter by grade level.
     */
    public function scopeByGradeLevel(Builder $query, string $gradeLevel): void
    {
        $query->where('grade_level', $gradeLevel);
    }

    /**
     * Scope a query to filter by section.
     */
    public function scopeBySection(Builder $query, string $section): void
    {
        $query->where('section', $section);
    }

    // CUSTOM METHODS

    /**
     * Generate a unique QR code hash for the student.
     */
    public function generateQRCode(): void
    {
        $data = $this->student_id . $this->first_name . $this->last_name . now()->timestamp;
        $this->qr_code_hash = Hash::make($data);
    }

    /**
     * Get the student's attendance rate as a percentage.
     */
    public function getAttendanceRate(int $days = 30): float
    {
        $totalDays = $this->attendance()
                          ->where('date', '>=', now()->subDays($days))
                          ->count();

        if ($totalDays === 0) {
            return 0.0;
        }

        $presentDays = $this->attendance()
                            ->where('date', '>=', now()->subDays($days))
                            ->where('status', 'present')
                            ->count();

        return round(($presentDays / $totalDays) * 100, 2);
    }

    /**
     * Determine if the student is at risk based on attendance.
     */
    public function isAtRisk(): bool
    {
        $attendanceRate = $this->getAttendanceRate();
        $recentAbsences = $this->attendance()
                               ->where('date', '>=', now()->subDays(7))
                               ->where('status', 'absent')
                               ->count();

        return $attendanceRate < 75 || $recentAbsences >= 3;
    }

    /**
     * Get validation rules for the model.
     */
    public static function validationRules(): array
    {
        return [
            'student_id' => ['required', 'string', 'max:50', 'unique:students,student_id'],
            'first_name' => ['required', 'string', 'max:100'],
            'last_name' => ['required', 'string', 'max:100'],
            'middle_name' => ['nullable', 'string', 'max:100'],
            'grade_level' => ['required', 'in:11,12'],
            'section' => ['required', 'string', 'max:50'],
            'parent_phone' => ['required', 'string', 'max:15'],
            'emergency_contact' => ['nullable', 'string', 'max:15'],
            'photo_path' => ['nullable', 'string', 'max:255'],
            'status' => ['required', 'in:' . implode(',', StudentStatus::values())],
        ];
    }
}
