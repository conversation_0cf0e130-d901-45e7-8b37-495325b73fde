<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('teachers', function (Blueprint $table) {
            // Primary key
            $table->id();
            
            // Teacher identification
            $table->string('teacher_id')->unique()->comment('Unique teacher identifier');
            
            // Personal information
            $table->string('first_name', 100);
            $table->string('last_name', 100);
            $table->string('phone_number', 15);
            
            // Authentication fields
            $table->string('email')->unique()->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->rememberToken();
            
            // Role and permissions
            $table->enum('role', ['teacher', 'admin', 'principal'])->default('teacher');
            
            // Academic assignments
            $table->json('assigned_sections')->nullable()->comment('JSON array of assigned sections');
            
            // Status and activity tracking
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_login_at')->nullable();
            
            // Timestamps
            $table->timestamps();
            
            // Indexes for performance optimization
            $table->index('role'); // Role-based queries
            $table->index('is_active'); // Active teacher filtering
            $table->index(['last_name', 'first_name']); // Name searches
            $table->index('last_login_at'); // Activity tracking
            $table->index('created_at'); // Date-based queries
        });

        // Create teacher-specific password reset tokens table
        Schema::create('teacher_password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
            
            // Index for token cleanup
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('teacher_password_reset_tokens');
        Schema::dropIfExists('teachers');
    }
};
