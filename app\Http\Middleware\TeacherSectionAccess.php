<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class TeacherSectionAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $teacher = Auth::guard('teacher')->user();

        if (!$teacher) {
            if ($request->expectsJson()) {
                return response()->json(['message' => 'Unauthenticated.'], 401);
            }
            
            return redirect()->route('teacher.login');
        }

        // Get section from route parameter or request
        $section = $request->route('section') ?? $request->input('section');

        if ($section && !$teacher->canAccessSection($section)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Access denied to this section.',
                    'section' => $section,
                    'assigned_sections' => $teacher->assigned_sections,
                ], 403);
            }
            
            abort(403, "Access denied to section: {$section}");
        }

        return $next($request);
    }
}
