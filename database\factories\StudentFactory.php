<?php

namespace Database\Factories;

use App\Enums\StudentStatus;
use App\Models\Student;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Student>
 */
class StudentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var class-string<\Illuminate\Database\Eloquent\Model>
     */
    protected $model = Student::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $firstName = fake()->firstName();
        $lastName = fake()->lastName();
        $middleName = fake()->optional(0.7)->firstName(); // 70% chance of having middle name
        
        return [
            'student_id' => $this->generateStudentId(),
            'first_name' => $firstName,
            'last_name' => $lastName,
            'middle_name' => $middleName,
            'grade_level' => fake()->randomElement(['11', '12']),
            'section' => fake()->randomElement(['A', 'B', 'C', 'D', 'E']),
            'parent_phone' => $this->generatePhilippinePhone(),
            'emergency_contact' => fake()->optional(0.8)->numerify('09#########'),
            'qr_code_hash' => Hash::make($firstName . $lastName . fake()->unixTime()),
            'photo_path' => fake()->optional(0.6)->imageUrl(200, 200, 'people'),
            'status' => fake()->randomElement(StudentStatus::cases()),
            'created_at' => fake()->dateTimeBetween('-2 years', 'now'),
        ];
    }

    /**
     * Generate a realistic DepEd student ID.
     */
    private function generateStudentId(): string
    {
        // Format: YYYY-NNNNNN (Year + 6-digit number)
        $year = fake()->numberBetween(2020, 2024);
        $number = fake()->unique()->numberBetween(100000, 999999);
        
        return $year . '-' . $number;
    }

    /**
     * Generate a Philippine mobile phone number.
     */
    private function generatePhilippinePhone(): string
    {
        $prefixes = ['0915', '0916', '0917', '0918', '0919', '0920', '0921', '0922', '0923', '0924', '0925', '0926', '0927', '0928', '0929', '0930', '0931', '0932', '0933', '0934', '0935', '0936', '0937', '0938', '0939', '0940', '0941', '0942', '0943', '0944', '0945', '0946', '0947', '0948', '0949', '0950'];
        
        $prefix = fake()->randomElement($prefixes);
        $suffix = fake()->numerify('#######');
        
        return $prefix . $suffix;
    }

    /**
     * Indicate that the student is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => StudentStatus::ACTIVE,
        ]);
    }

    /**
     * Indicate that the student is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => StudentStatus::INACTIVE,
        ]);
    }

    /**
     * Indicate that the student has transferred.
     */
    public function transferred(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => StudentStatus::TRANSFERRED,
        ]);
    }

    /**
     * Indicate that the student has graduated.
     */
    public function graduated(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => StudentStatus::GRADUATED,
        ]);
    }

    /**
     * Create a Grade 11 student.
     */
    public function grade11(): static
    {
        return $this->state(fn (array $attributes) => [
            'grade_level' => '11',
        ]);
    }

    /**
     * Create a Grade 12 student.
     */
    public function grade12(): static
    {
        return $this->state(fn (array $attributes) => [
            'grade_level' => '12',
        ]);
    }

    /**
     * Create a student in a specific section.
     */
    public function inSection(string $section): static
    {
        return $this->state(fn (array $attributes) => [
            'section' => $section,
        ]);
    }

    /**
     * Create a student without a middle name.
     */
    public function withoutMiddleName(): static
    {
        return $this->state(fn (array $attributes) => [
            'middle_name' => null,
        ]);
    }

    /**
     * Create a student without emergency contact.
     */
    public function withoutEmergencyContact(): static
    {
        return $this->state(fn (array $attributes) => [
            'emergency_contact' => null,
        ]);
    }

    /**
     * Create a student without photo.
     */
    public function withoutPhoto(): static
    {
        return $this->state(fn (array $attributes) => [
            'photo_path' => null,
        ]);
    }
}
