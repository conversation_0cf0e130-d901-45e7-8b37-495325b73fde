<?php

namespace Database\Factories;

use App\Enums\AttendanceStatus;
use App\Models\Attendance;
use App\Models\Student;
use App\Models\Subject;
use App\Models\Teacher;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Attendance>
 */
class AttendanceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var class-string<\Illuminate\Database\Eloquent\Model>
     */
    protected $model = Attendance::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $date = fake()->dateTimeBetween('-30 days', 'now');
        $status = fake()->randomElement(AttendanceStatus::cases());
        
        // Generate realistic time_in and time_out based on status
        [$timeIn, $timeOut] = $this->generateTimes($status, $date);

        return [
            'student_id' => Student::factory(),
            'teacher_id' => Teacher::factory(),
            'subject_id' => Subject::factory(),
            'date' => $date,
            'time_in' => $timeIn,
            'time_out' => $timeOut,
            'status' => $status,
            'remarks' => fake()->optional(0.3)->sentence(),
            'ip_address' => fake()->optional(0.7)->ipv4(),
            'device_info' => fake()->optional(0.8)->randomElement([
                [
                    'device_type' => 'mobile',
                    'browser' => 'Chrome Mobile',
                    'os' => 'Android',
                    'user_agent' => fake()->userAgent(),
                ],
                [
                    'device_type' => 'tablet',
                    'browser' => 'Safari',
                    'os' => 'iOS',
                    'user_agent' => fake()->userAgent(),
                ],
                [
                    'device_type' => 'desktop',
                    'browser' => 'Chrome',
                    'os' => 'Windows',
                    'user_agent' => fake()->userAgent(),
                ],
            ]),
        ];
    }

    /**
     * Generate realistic time_in and time_out based on status.
     */
    private function generateTimes(AttendanceStatus $status, \DateTime $date): array
    {
        $dateString = $date->format('Y-m-d');

        return match($status) {
            AttendanceStatus::PRESENT => [
                Carbon::parse($dateString . ' ' . fake()->time('H:i:s', '08:15:00')),
                Carbon::parse($dateString . ' ' . fake()->time('H:i:s', '17:00:00')),
            ],
            AttendanceStatus::LATE => [
                Carbon::parse($dateString . ' ' . fake()->timeBetween('08:16:00', '09:30:00')->format('H:i:s')),
                Carbon::parse($dateString . ' ' . fake()->time('H:i:s', '17:00:00')),
            ],
            AttendanceStatus::EXCUSED => [
                fake()->boolean(70) ? Carbon::parse($dateString . ' ' . fake()->time('H:i:s', '08:15:00')) : null,
                fake()->boolean(70) ? Carbon::parse($dateString . ' ' . fake()->time('H:i:s', '17:00:00')) : null,
            ],
            AttendanceStatus::ABSENT => [null, null],
        };
    }

    /**
     * Create a present attendance record.
     */
    public function present(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => AttendanceStatus::PRESENT,
            'time_in' => Carbon::parse($attributes['date']->format('Y-m-d') . ' ' . fake()->time('H:i:s', '08:15:00')),
            'time_out' => Carbon::parse($attributes['date']->format('Y-m-d') . ' ' . fake()->time('H:i:s', '17:00:00')),
        ]);
    }

    /**
     * Create an absent attendance record.
     */
    public function absent(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => AttendanceStatus::ABSENT,
            'time_in' => null,
            'time_out' => null,
            'remarks' => fake()->randomElement([
                'Sick leave',
                'Family emergency',
                'Medical appointment',
                'Unexcused absence',
            ]),
        ]);
    }

    /**
     * Create a late attendance record.
     */
    public function late(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => AttendanceStatus::LATE,
            'time_in' => Carbon::parse($attributes['date']->format('Y-m-d') . ' ' . fake()->timeBetween('08:16:00', '09:30:00')->format('H:i:s')),
            'time_out' => Carbon::parse($attributes['date']->format('Y-m-d') . ' ' . fake()->time('H:i:s', '17:00:00')),
            'remarks' => fake()->randomElement([
                'Traffic jam',
                'Transportation delay',
                'Overslept',
                'Family matter',
            ]),
        ]);
    }

    /**
     * Create an excused attendance record.
     */
    public function excused(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => AttendanceStatus::EXCUSED,
            'remarks' => fake()->randomElement([
                'School activity',
                'Medical appointment',
                'Family emergency',
                'Official school business',
            ]),
        ]);
    }

    /**
     * Create attendance for today.
     */
    public function today(): static
    {
        return $this->state(fn (array $attributes) => [
            'date' => today(),
        ]);
    }

    /**
     * Create attendance for a specific date.
     */
    public function forDate(string $date): static
    {
        return $this->state(fn (array $attributes) => [
            'date' => Carbon::parse($date),
        ]);
    }

    /**
     * Create attendance for a specific student.
     */
    public function forStudent(int $studentId): static
    {
        return $this->state(fn (array $attributes) => [
            'student_id' => $studentId,
        ]);
    }

    /**
     * Create attendance with complete time records.
     */
    public function complete(): static
    {
        return $this->state(function (array $attributes) {
            $dateString = $attributes['date']->format('Y-m-d');
            return [
                'time_in' => Carbon::parse($dateString . ' ' . fake()->time('H:i:s', '08:15:00')),
                'time_out' => Carbon::parse($dateString . ' ' . fake()->time('H:i:s', '17:00:00')),
            ];
        });
    }

    /**
     * Create attendance with only time_in (no time_out).
     */
    public function timeInOnly(): static
    {
        return $this->state(function (array $attributes) {
            $dateString = $attributes['date']->format('Y-m-d');
            return [
                'time_in' => Carbon::parse($dateString . ' ' . fake()->time('H:i:s', '08:15:00')),
                'time_out' => null,
            ];
        });
    }
}
