<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class TeacherAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!Auth::guard('teacher')->check()) {
            if ($request->expectsJson()) {
                return response()->json(['message' => 'Unauthenticated.'], 401);
            }
            
            return redirect()->route('teacher.login');
        }

        $teacher = Auth::guard('teacher')->user();

        // Check if teacher is active
        if (!$teacher->is_active) {
            Auth::guard('teacher')->logout();
            
            if ($request->expectsJson()) {
                return response()->json(['message' => 'Account is inactive.'], 403);
            }
            
            return redirect()->route('teacher.login')
                           ->withErrors(['email' => 'Your account has been deactivated.']);
        }

        return $next($request);
    }
}
