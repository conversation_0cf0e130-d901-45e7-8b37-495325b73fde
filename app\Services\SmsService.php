<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SmsService
{
    /**
     * The SMS provider configuration.
     */
    protected array $config;

    /**
     * Create a new SMS service instance.
     */
    public function __construct()
    {
        $this->config = config('services.sms', []);
    }

    /**
     * Send an SMS message.
     */
    public function send(string $phoneNumber, string $message): bool
    {
        // Clean phone number
        $phoneNumber = $this->cleanPhoneNumber($phoneNumber);

        // Validate phone number
        if (!$this->isValidPhoneNumber($phoneNumber)) {
            throw new \InvalidArgumentException("Invalid phone number: {$phoneNumber}");
        }

        // Choose SMS provider based on configuration
        return match($this->config['provider'] ?? 'log') {
            'semaphore' => $this->sendViaSemaphore($phoneNumber, $message),
            'twilio' => $this->sendViaTwilio($phoneNumber, $message),
            'itexmo' => $this->sendViaItexmo($phoneNumber, $message),
            default => $this->sendViaLog($phoneNumber, $message),
        };
    }

    /**
     * Send SMS via Semaphore (Popular in Philippines).
     */
    protected function sendViaSemaphore(string $phoneNumber, string $message): bool
    {
        $response = Http::post('https://api.semaphore.co/api/v4/messages', [
            'apikey' => $this->config['semaphore']['api_key'],
            'number' => $phoneNumber,
            'message' => $message,
            'sendername' => $this->config['semaphore']['sender_name'] ?? 'SCHOOL',
        ]);

        if ($response->successful()) {
            Log::info('SMS sent via Semaphore', [
                'phone' => $phoneNumber,
                'response' => $response->json(),
            ]);
            return true;
        }

        Log::error('Failed to send SMS via Semaphore', [
            'phone' => $phoneNumber,
            'response' => $response->body(),
            'status' => $response->status(),
        ]);

        return false;
    }

    /**
     * Send SMS via Twilio.
     */
    protected function sendViaTwilio(string $phoneNumber, string $message): bool
    {
        $accountSid = $this->config['twilio']['account_sid'];
        $authToken = $this->config['twilio']['auth_token'];
        $fromNumber = $this->config['twilio']['from_number'];

        $response = Http::withBasicAuth($accountSid, $authToken)
            ->asForm()
            ->post("https://api.twilio.com/2010-04-01/Accounts/{$accountSid}/Messages.json", [
                'From' => $fromNumber,
                'To' => $phoneNumber,
                'Body' => $message,
            ]);

        if ($response->successful()) {
            Log::info('SMS sent via Twilio', [
                'phone' => $phoneNumber,
                'response' => $response->json(),
            ]);
            return true;
        }

        Log::error('Failed to send SMS via Twilio', [
            'phone' => $phoneNumber,
            'response' => $response->body(),
            'status' => $response->status(),
        ]);

        return false;
    }

    /**
     * Send SMS via iTexMo (Popular in Philippines).
     */
    protected function sendViaItexmo(string $phoneNumber, string $message): bool
    {
        $response = Http::post('https://www.itexmo.com/php_api/api.php', [
            '1' => $phoneNumber,
            '2' => $message,
            '3' => $this->config['itexmo']['api_code'],
            'passwd' => $this->config['itexmo']['password'],
        ]);

        $result = $response->body();

        if ($result === '0') { // iTexMo returns '0' for success
            Log::info('SMS sent via iTexMo', [
                'phone' => $phoneNumber,
                'result' => $result,
            ]);
            return true;
        }

        Log::error('Failed to send SMS via iTexMo', [
            'phone' => $phoneNumber,
            'result' => $result,
        ]);

        return false;
    }

    /**
     * Log SMS instead of sending (for development/testing).
     */
    protected function sendViaLog(string $phoneNumber, string $message): bool
    {
        Log::info('SMS would be sent', [
            'phone' => $phoneNumber,
            'message' => $message,
        ]);

        return true;
    }

    /**
     * Clean and format phone number.
     */
    protected function cleanPhoneNumber(string $phoneNumber): string
    {
        // Remove all non-numeric characters
        $cleaned = preg_replace('/[^0-9]/', '', $phoneNumber);

        // Add Philippines country code if not present
        if (strlen($cleaned) === 10 && !str_starts_with($cleaned, '63')) {
            $cleaned = '63' . $cleaned;
        }

        return '+' . $cleaned;
    }

    /**
     * Validate Philippine phone number format.
     */
    protected function isValidPhoneNumber(string $phoneNumber): bool
    {
        // Philippine mobile numbers: +639XXXXXXXXX (13 digits total)
        return preg_match('/^\+639\d{9}$/', $phoneNumber) === 1;
    }

    /**
     * Get SMS provider status.
     */
    public function getProviderStatus(): array
    {
        return [
            'provider' => $this->config['provider'] ?? 'log',
            'configured' => !empty($this->config),
            'available_providers' => ['semaphore', 'twilio', 'itexmo', 'log'],
        ];
    }
}
