<?php

namespace App\Enums;

enum TeacherRole: string
{
    case TEACHER = 'teacher';
    case ADMIN = 'admin';
    case PRINCIPAL = 'principal';

    /**
     * Get all enum values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get human-readable labels
     */
    public function label(): string
    {
        return match($this) {
            self::TEACHER => 'Teacher',
            self::ADMIN => 'Administrator',
            self::PRINCIPAL => 'Principal',
        };
    }

    /**
     * Get role hierarchy level (higher number = more permissions)
     */
    public function level(): int
    {
        return match($this) {
            self::TEACHER => 1,
            self::ADMIN => 2,
            self::PRINCIPAL => 3,
        };
    }

    /**
     * Get permissions for this role
     */
    public function permissions(): array
    {
        return match($this) {
            self::TEACHER => [
                'view_own_attendance',
                'mark_attendance',
                'view_assigned_students',
                'send_sms_to_parents',
            ],
            self::ADMIN => [
                'view_own_attendance',
                'mark_attendance',
                'view_assigned_students',
                'send_sms_to_parents',
                'view_all_attendance',
                'manage_students',
                'view_reports',
                'manage_sections',
            ],
            self::PRINCIPAL => [
                'view_own_attendance',
                'mark_attendance',
                'view_assigned_students',
                'send_sms_to_parents',
                'view_all_attendance',
                'manage_students',
                'view_reports',
                'manage_sections',
                'manage_teachers',
                'system_settings',
                'full_access',
            ],
        };
    }

    /**
     * Check if this role can access another role's data
     */
    public function canAccess(TeacherRole $targetRole): bool
    {
        return $this->level() >= $targetRole->level();
    }
}
