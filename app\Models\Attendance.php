<?php

namespace App\Models;

use App\Enums\AttendanceStatus;
use App\Events\AttendanceRecorded;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Attendance extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'student_id',
        'teacher_id',
        'subject_id',
        'date',
        'time_in',
        'time_out',
        'status',
        'remarks',
        'ip_address',
        'device_info',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'date' => 'date',
            'time_in' => 'datetime:H:i:s',
            'time_out' => 'datetime:H:i:s',
            'status' => AttendanceStatus::class,
            'device_info' => 'array',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        static::created(function (Attendance $attendance) {
            event(new AttendanceRecorded($attendance));
        });

        static::updated(function (Attendance $attendance) {
            if ($attendance->wasChanged('status')) {
                event(new AttendanceRecorded($attendance));
            }
        });
    }

    // RELATIONSHIPS

    /**
     * Get the student that owns the attendance record.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the teacher that owns the attendance record.
     */
    public function teacher(): BelongsTo
    {
        return $this->belongsTo(Teacher::class);
    }

    /**
     * Get the subject that owns the attendance record.
     */
    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    // SCOPES

    /**
     * Scope a query to only include today's attendance.
     */
    public function scopeToday(Builder $query): void
    {
        $query->whereDate('date', today());
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeByDateRange(Builder $query, string $startDate, string $endDate): void
    {
        $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * Scope a query to filter by status.
     */
    public function scopeByStatus(Builder $query, AttendanceStatus $status): void
    {
        $query->where('status', $status);
    }

    /**
     * Scope a query to filter by student.
     */
    public function scopeByStudent(Builder $query, int $studentId): void
    {
        $query->where('student_id', $studentId);
    }

    /**
     * Scope a query to filter by present status (including late and excused).
     */
    public function scopePresent(Builder $query): void
    {
        $query->whereIn('status', [
            AttendanceStatus::PRESENT,
            AttendanceStatus::LATE,
            AttendanceStatus::EXCUSED
        ]);
    }

    /**
     * Scope a query to filter by absent status.
     */
    public function scopeAbsent(Builder $query): void
    {
        $query->where('status', AttendanceStatus::ABSENT);
    }

    // ACCESSORS

    /**
     * Get the duration between time_in and time_out.
     */
    public function getDurationAttribute(): ?string
    {
        if (!$this->time_in || !$this->time_out) {
            return null;
        }

        $timeIn = Carbon::parse($this->time_in);
        $timeOut = Carbon::parse($this->time_out);

        $duration = $timeOut->diff($timeIn);

        return sprintf('%02d:%02d:%02d',
            $duration->h,
            $duration->i,
            $duration->s
        );
    }

    /**
     * Check if the student is late based on school policy.
     */
    public function getIsLateAttribute(): bool
    {
        if (!$this->time_in) {
            return false;
        }

        // School policy: Classes start at 8:00 AM, late after 8:15 AM
        $dateString = $this->date instanceof Carbon ? $this->date->format('Y-m-d') : $this->date;
        $lateThreshold = Carbon::parse($dateString . ' 08:15:00');
        $timeIn = Carbon::parse($this->time_in);

        return $timeIn->gt($lateThreshold);
    }

    /**
     * Get formatted time in for display.
     */
    public function getFormattedTimeInAttribute(): ?string
    {
        return $this->time_in ? Carbon::parse($this->time_in)->format('h:i A') : null;
    }

    /**
     * Get formatted time out for display.
     */
    public function getFormattedTimeOutAttribute(): ?string
    {
        return $this->time_out ? Carbon::parse($this->time_out)->format('h:i A') : null;
    }

    // CUSTOM METHODS

    /**
     * Mark the attendance as present.
     */
    public function markPresent(?string $timeIn = null, ?string $remarks = null): bool
    {
        $timeIn = $timeIn ?: now()->format('H:i:s');

        $this->update([
            'status' => $this->isLate ? AttendanceStatus::LATE : AttendanceStatus::PRESENT,
            'time_in' => $timeIn,
            'remarks' => $remarks,
        ]);

        return true;
    }

    /**
     * Mark the attendance as absent.
     */
    public function markAbsent(?string $remarks = null): bool
    {
        $this->update([
            'status' => AttendanceStatus::ABSENT,
            'time_in' => null,
            'time_out' => null,
            'remarks' => $remarks,
        ]);

        return true;
    }

    /**
     * Calculate duration in minutes.
     */
    public function calculateDuration(): ?int
    {
        if (!$this->time_in || !$this->time_out) {
            return null;
        }

        $timeIn = Carbon::parse($this->time_in);
        $timeOut = Carbon::parse($this->time_out);

        return $timeOut->diffInMinutes($timeIn);
    }

    /**
     * Mark time out for the attendance.
     */
    public function markTimeOut(?string $timeOut = null): bool
    {
        $timeOut = $timeOut ?: now()->format('H:i:s');

        $this->update([
            'time_out' => $timeOut,
        ]);

        return true;
    }

    /**
     * Check if attendance is complete (has both time in and time out).
     */
    public function isComplete(): bool
    {
        return !is_null($this->time_in) && !is_null($this->time_out);
    }

    /**
     * Get validation rules for the model.
     */
    public static function validationRules(): array
    {
        return [
            'student_id' => ['required', 'exists:students,id'],
            'teacher_id' => ['required', 'exists:teachers,id'],
            'subject_id' => ['required', 'exists:subjects,id'],
            'date' => ['required', 'date'],
            'time_in' => ['nullable', 'date_format:H:i:s'],
            'time_out' => ['nullable', 'date_format:H:i:s', 'after:time_in'],
            'status' => ['required', 'in:' . implode(',', AttendanceStatus::values())],
            'remarks' => ['nullable', 'string', 'max:1000'],
            'ip_address' => ['nullable', 'ip'],
            'device_info' => ['nullable', 'array'],
        ];
    }
}
