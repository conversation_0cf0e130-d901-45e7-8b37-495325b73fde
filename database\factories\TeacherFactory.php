<?php

namespace Database\Factories;

use App\Models\Teacher;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Teacher>
 */
class TeacherFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var class-string<\Illuminate\Database\Eloquent\Model>
     */
    protected $model = Teacher::class;

    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'teacher_id' => $this->generateTeacherId(),
            'first_name' => fake()->firstName(),
            'last_name' => fake()->lastName(),
            'phone_number' => $this->generatePhilippinePhone(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => fake()->boolean(80) ? now() : null,
            'password' => static::$password ??= Hash::make('password'),
            'remember_token' => Str::random(10),
            'role' => fake()->randomElement(['teacher', 'admin', 'principal']),
            'assigned_sections' => fake()->randomElements(['A', 'B', 'C', 'D', 'E'], fake()->numberBetween(1, 3)),
            'is_active' => fake()->boolean(90),
            'last_login_at' => fake()->optional(0.8)->dateTimeBetween('-30 days', 'now'),
        ];
    }

    /**
     * Generate a realistic teacher ID.
     */
    private function generateTeacherId(): string
    {
        // Format: T-YYYY-NNNN (T + Year + 4-digit number)
        $year = fake()->numberBetween(2020, 2024);
        $number = fake()->unique()->numberBetween(1000, 9999);
        
        return 'T-' . $year . '-' . $number;
    }

    /**
     * Generate a Philippine mobile phone number.
     */
    private function generatePhilippinePhone(): string
    {
        $prefixes = ['0915', '0916', '0917', '0918', '0919', '0920', '0921', '0922', '0923', '0924', '0925', '0926', '0927', '0928', '0929', '0930', '0931', '0932', '0933', '0934', '0935', '0936', '0937', '0938', '0939', '0940', '0941', '0942', '0943', '0944', '0945', '0946', '0947', '0948', '0949', '0950'];
        
        $prefix = fake()->randomElement($prefixes);
        $suffix = fake()->numerify('#######');
        
        return $prefix . $suffix;
    }

    /**
     * Create a teacher with teacher role.
     */
    public function teacher(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'teacher',
        ]);
    }

    /**
     * Create a teacher with admin role.
     */
    public function admin(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'admin',
        ]);
    }

    /**
     * Create a teacher with principal role.
     */
    public function principal(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'principal',
        ]);
    }

    /**
     * Create an active teacher.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Create an inactive teacher.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a teacher with verified email.
     */
    public function verified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => now(),
        ]);
    }

    /**
     * Create a teacher with unverified email.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }
}
