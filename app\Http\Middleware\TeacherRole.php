<?php

namespace App\Http\Middleware;

use App\Enums\TeacherRole as TeacherRoleEnum;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class TeacherRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$roles): Response
    {
        $teacher = Auth::guard('teacher')->user();

        if (!$teacher) {
            if ($request->expectsJson()) {
                return response()->json(['message' => 'Unauthenticated.'], 401);
            }
            
            return redirect()->route('teacher.login');
        }

        // Convert string roles to enum cases
        $requiredRoles = array_map(function ($role) {
            return TeacherRoleEnum::from($role);
        }, $roles);

        // Check if teacher has any of the required roles
        if (!empty($requiredRoles) && !in_array($teacher->role, $requiredRoles)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Insufficient role permissions.',
                    'required_roles' => array_map(fn($role) => $role->value, $requiredRoles),
                    'user_role' => $teacher->role->value,
                ], 403);
            }
            
            abort(403, 'Insufficient role permissions to access this resource.');
        }

        return $next($request);
    }
}
