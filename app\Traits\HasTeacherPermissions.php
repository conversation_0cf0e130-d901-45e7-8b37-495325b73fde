<?php

namespace App\Traits;

use App\Enums\TeacherRole;
use App\Models\Teacher;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

trait HasTeacherPermissions
{
    /**
     * Get the authenticated teacher.
     */
    protected function getAuthenticatedTeacher(): ?Teacher
    {
        return Auth::guard('teacher')->user();
    }

    /**
     * Check if the authenticated teacher has a specific permission.
     */
    protected function hasPermission(string $permission): bool
    {
        $teacher = $this->getAuthenticatedTeacher();
        
        return $teacher && $teacher->hasPermission($permission);
    }

    /**
     * Check if the authenticated teacher has any of the given permissions.
     */
    protected function hasAnyPermission(array $permissions): bool
    {
        $teacher = $this->getAuthenticatedTeacher();
        
        return $teacher && $teacher->hasAnyPermission($permissions);
    }

    /**
     * Check if the authenticated teacher has all of the given permissions.
     */
    protected function hasAllPermissions(array $permissions): bool
    {
        $teacher = $this->getAuthenticatedTeacher();
        
        return $teacher && $teacher->hasAllPermissions($permissions);
    }

    /**
     * Check if the authenticated teacher has a specific role.
     */
    protected function hasRole(TeacherRole $role): bool
    {
        $teacher = $this->getAuthenticatedTeacher();
        
        return $teacher && $teacher->role === $role;
    }

    /**
     * Check if the authenticated teacher has any of the given roles.
     */
    protected function hasAnyRole(array $roles): bool
    {
        $teacher = $this->getAuthenticatedTeacher();
        
        return $teacher && in_array($teacher->role, $roles);
    }

    /**
     * Check if the authenticated teacher can access a specific section.
     */
    protected function canAccessSection(string $section): bool
    {
        $teacher = $this->getAuthenticatedTeacher();
        
        return $teacher && $teacher->canAccessSection($section);
    }

    /**
     * Check if the authenticated teacher can manage another teacher.
     */
    protected function canManageTeacher(Teacher $targetTeacher): bool
    {
        $teacher = $this->getAuthenticatedTeacher();
        
        return $teacher && $teacher->canManage($targetTeacher);
    }

    /**
     * Abort with 403 if the teacher doesn't have the required permission.
     */
    protected function requirePermission(string $permission): void
    {
        if (!$this->hasPermission($permission)) {
            abort(403, "Permission required: {$permission}");
        }
    }

    /**
     * Abort with 403 if the teacher doesn't have any of the required permissions.
     */
    protected function requireAnyPermission(array $permissions): void
    {
        if (!$this->hasAnyPermission($permissions)) {
            $permissionList = implode(', ', $permissions);
            abort(403, "One of these permissions required: {$permissionList}");
        }
    }

    /**
     * Abort with 403 if the teacher doesn't have the required role.
     */
    protected function requireRole(TeacherRole $role): void
    {
        if (!$this->hasRole($role)) {
            abort(403, "Role required: {$role->label()}");
        }
    }

    /**
     * Abort with 403 if the teacher doesn't have any of the required roles.
     */
    protected function requireAnyRole(array $roles): void
    {
        if (!$this->hasAnyRole($roles)) {
            $roleLabels = array_map(fn($role) => $role->label(), $roles);
            $roleList = implode(', ', $roleLabels);
            abort(403, "One of these roles required: {$roleList}");
        }
    }

    /**
     * Abort with 403 if the teacher can't access the section.
     */
    protected function requireSectionAccess(string $section): void
    {
        if (!$this->canAccessSection($section)) {
            abort(403, "Access denied to section: {$section}");
        }
    }

    /**
     * Return a JSON response for permission denied.
     */
    protected function permissionDeniedResponse(string $message = 'Permission denied'): JsonResponse
    {
        return response()->json([
            'message' => $message,
            'teacher_permissions' => $this->getAuthenticatedTeacher()?->getAllPermissions() ?? [],
        ], 403);
    }

    /**
     * Return a JSON response for role access denied.
     */
    protected function roleAccessDeniedResponse(string $message = 'Insufficient role permissions'): JsonResponse
    {
        $teacher = $this->getAuthenticatedTeacher();
        
        return response()->json([
            'message' => $message,
            'teacher_role' => $teacher?->role->value,
            'teacher_permissions' => $teacher?->getAllPermissions() ?? [],
        ], 403);
    }

    /**
     * Return a JSON response for section access denied.
     */
    protected function sectionAccessDeniedResponse(string $section): JsonResponse
    {
        $teacher = $this->getAuthenticatedTeacher();
        
        return response()->json([
            'message' => "Access denied to section: {$section}",
            'assigned_sections' => $teacher?->assigned_sections ?? [],
        ], 403);
    }
}
