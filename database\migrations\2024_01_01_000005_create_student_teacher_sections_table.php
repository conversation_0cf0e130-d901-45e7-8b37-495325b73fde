<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_teacher_sections', function (Blueprint $table) {
            $table->id();
            
            // Foreign keys
            $table->foreignId('student_id')
                  ->constrained('students')
                  ->onDelete('cascade');
                  
            $table->foreignId('teacher_id')
                  ->constrained('teachers')
                  ->onDelete('cascade');
                  
            $table->foreignId('subject_id')
                  ->constrained('subjects')
                  ->onDelete('cascade');
            
            // Additional pivot data
            $table->string('section', 50);
            
            // Timestamps
            $table->timestamps();
            
            // Indexes
            $table->index(['student_id', 'teacher_id']);
            $table->index(['teacher_id', 'section']);
            $table->index(['student_id', 'section']);
            
            // Unique constraint to prevent duplicate assignments
            $table->unique(['student_id', 'teacher_id', 'subject_id', 'section'], 
                          'unique_student_teacher_subject_section');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_teacher_sections');
    }
};
