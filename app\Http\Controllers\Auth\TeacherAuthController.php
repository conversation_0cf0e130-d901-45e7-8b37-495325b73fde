<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Teacher;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class TeacherAuthController extends Controller
{
    /**
     * Handle teacher login request.
     */
    public function login(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => ['required', 'email'],
            'password' => ['required', 'string'],
            'remember' => ['boolean'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $credentials = $request->only('email', 'password');
        $remember = $request->boolean('remember');

        // Attempt authentication
        if (!Auth::guard('teacher')->attempt($credentials, $remember)) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        $teacher = Auth::guard('teacher')->user();

        // Check if teacher is active
        if (!$teacher->is_active) {
            Auth::guard('teacher')->logout();
            
            return response()->json([
                'message' => 'Your account has been deactivated.',
            ], 403);
        }

        // Update last login
        $teacher->update(['last_login_at' => now()]);

        // Create API token for mobile/API access
        $token = $teacher->createApiToken('mobile-app');

        return response()->json([
            'message' => 'Login successful',
            'teacher' => [
                'id' => $teacher->id,
                'teacher_id' => $teacher->teacher_id,
                'full_name' => $teacher->full_name,
                'email' => $teacher->email,
                'role' => $teacher->role->value,
                'role_label' => $teacher->role->label(),
                'assigned_sections' => $teacher->assigned_sections,
                'permissions' => $teacher->getAllPermissions(),
                'last_login_at' => $teacher->last_login_at,
            ],
            'token' => $token,
        ]);
    }

    /**
     * Handle teacher logout request.
     */
    public function logout(Request $request): JsonResponse
    {
        $teacher = Auth::guard('teacher')->user();
        
        if ($teacher) {
            // Revoke all tokens
            $teacher->revokeAllTokens();
        }

        Auth::guard('teacher')->logout();

        return response()->json([
            'message' => 'Logout successful',
        ]);
    }

    /**
     * Get authenticated teacher information.
     */
    public function me(Request $request): JsonResponse
    {
        $teacher = Auth::guard('teacher')->user();

        return response()->json([
            'teacher' => [
                'id' => $teacher->id,
                'teacher_id' => $teacher->teacher_id,
                'full_name' => $teacher->full_name,
                'first_name' => $teacher->first_name,
                'last_name' => $teacher->last_name,
                'email' => $teacher->email,
                'phone_number' => $teacher->phone_number,
                'formatted_phone' => $teacher->formatted_phone,
                'role' => $teacher->role->value,
                'role_label' => $teacher->role->label(),
                'assigned_sections' => $teacher->assigned_sections,
                'permissions' => $teacher->getAllPermissions(),
                'is_active' => $teacher->is_active,
                'is_online' => $teacher->is_online,
                'last_login_at' => $teacher->last_login_at,
                'created_at' => $teacher->created_at,
            ],
        ]);
    }

    /**
     * Update teacher profile.
     */
    public function updateProfile(Request $request): JsonResponse
    {
        $teacher = Auth::guard('teacher')->user();

        $validator = Validator::make($request->all(), [
            'first_name' => ['required', 'string', 'max:100'],
            'last_name' => ['required', 'string', 'max:100'],
            'phone_number' => ['required', 'string', 'max:20'],
            'email' => ['required', 'email', 'max:255', 'unique:teachers,email,' . $teacher->id],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $teacher->update($validator->validated());

        return response()->json([
            'message' => 'Profile updated successfully',
            'teacher' => [
                'id' => $teacher->id,
                'teacher_id' => $teacher->teacher_id,
                'full_name' => $teacher->full_name,
                'first_name' => $teacher->first_name,
                'last_name' => $teacher->last_name,
                'email' => $teacher->email,
                'phone_number' => $teacher->phone_number,
                'formatted_phone' => $teacher->formatted_phone,
            ],
        ]);
    }

    /**
     * Change teacher password.
     */
    public function changePassword(Request $request): JsonResponse
    {
        $teacher = Auth::guard('teacher')->user();

        $validator = Validator::make($request->all(), [
            'current_password' => ['required', 'string'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        // Verify current password
        if (!Hash::check($request->current_password, $teacher->password)) {
            return response()->json([
                'message' => 'Current password is incorrect',
                'errors' => [
                    'current_password' => ['The current password is incorrect.'],
                ],
            ], 422);
        }

        // Update password
        $teacher->update([
            'password' => Hash::make($request->password),
        ]);

        // Revoke all existing tokens to force re-login
        $teacher->revokeAllTokens();

        return response()->json([
            'message' => 'Password changed successfully. Please login again.',
        ]);
    }

    /**
     * Refresh API token.
     */
    public function refreshToken(Request $request): JsonResponse
    {
        $teacher = Auth::guard('teacher')->user();
        
        // Create new token
        $token = $teacher->createApiToken('mobile-app');

        return response()->json([
            'message' => 'Token refreshed successfully',
            'token' => $token,
        ]);
    }
}
