<?php

namespace Database\Factories;

use App\Models\Subject;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Subject>
 */
class SubjectFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var class-string<\Illuminate\Database\Eloquent\Model>
     */
    protected $model = Subject::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $subjects = [
            ['name' => 'Mathematics', 'code' => 'MATH'],
            ['name' => 'English', 'code' => 'ENG'],
            ['name' => 'Science', 'code' => 'SCI'],
            ['name' => 'Filipino', 'code' => 'FIL'],
            ['name' => 'Araling Panlipunan', 'code' => 'AP'],
            ['name' => 'Physical Education', 'code' => 'PE'],
            ['name' => 'Computer Science', 'code' => 'CS'],
            ['name' => 'Research', 'code' => 'RES'],
            ['name' => 'Statistics', 'code' => 'STAT'],
            ['name' => 'Physics', 'code' => 'PHYS'],
            ['name' => 'Chemistry', 'code' => 'CHEM'],
            ['name' => 'Biology', 'code' => 'BIO'],
        ];

        $subject = fake()->randomElement($subjects);
        $gradeLevel = fake()->randomElement(['11', '12']);

        return [
            'name' => $subject['name'],
            'code' => $subject['code'] . $gradeLevel,
            'description' => fake()->optional(0.7)->sentence(),
            'grade_level' => $gradeLevel,
            'is_active' => fake()->boolean(90), // 90% chance of being active
        ];
    }

    /**
     * Create an active subject.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Create an inactive subject.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a Grade 11 subject.
     */
    public function grade11(): static
    {
        return $this->state(fn (array $attributes) => [
            'grade_level' => '11',
        ]);
    }

    /**
     * Create a Grade 12 subject.
     */
    public function grade12(): static
    {
        return $this->state(fn (array $attributes) => [
            'grade_level' => '12',
        ]);
    }
}
